/* Settings Page Styles - Compact Design */

/* Input Toggle Styles */
.input-with-toggle {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-toggle .modal-input {
  padding-right: 50px;
  flex: 1;
}

.toggle-visibility-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
  z-index: 10;
}

.toggle-visibility-btn:hover {
  color: var(--primary-color);
  background: rgba(212, 175, 55, 0.1);
}

.toggle-visibility-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Account Info Styles */
.account-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.account-info h4 {
  color: var(--primary-color);
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.account-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.account-item:last-child {
  border-bottom: none;
}

.account-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
}

.account-item .value {
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  text-align: right;
}

.account-item .value.enabled {
  color: #10b981;
}

.account-item .value.disabled {
  color: #ef4444;
}

.account-item.highlighted {
  background: rgba(212, 175, 55, 0.1);
  border-radius: 6px;
  padding: 10px;
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.account-item.highlighted .label {
  color: var(--primary-color);
  font-weight: 600;
}

.account-item .label i {
  margin-right: 6px;
  width: 14px;
}

.account-item .value.user-id {
  font-family: 'Courier New', monospace;
  background: rgba(212, 175, 55, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  color: var(--primary-color);
  font-weight: bold;
}

.account-item .value.permissions {
  background: rgba(16, 185, 129, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: #10b981;
  font-size: 11px;
  font-weight: 600;
}

.account-item .value i {
  margin-right: 6px;
}

/* Permission badges grid layout */
.permissions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.permission-badge {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.permission-badge.enabled {
  background: rgba(16, 185, 129, 0.15);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.permission-badge.disabled {
  background: rgba(239, 68, 68, 0.15);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.permission-badge i {
  margin-right: 4px;
  font-size: 8px;
}

/* Commission rates display */
.commission-rates {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.commission-rates span {
  background: rgba(212, 175, 55, 0.1);
  padding: 3px 8px;
  border-radius: 4px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  color: var(--primary-color);
  font-size: 11px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Warning and error info items */
.info-item.warning {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
}

.info-item.warning i {
  color: #fbbf24;
}

.info-item.warning span {
  color: #fbbf24;
}

.info-item.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
}

.info-item.error i {
  color: #ef4444;
}

.info-item.error span {
  color: #ef4444;
}

/* Notification Modal Styles */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

.notification-modal {
  background: rgba(20, 20, 20, 0.95);
  border-radius: 12px;
  border: 2px solid;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.7);
  min-width: 400px;
  max-width: 500px;
  animation: notificationSlideIn 0.3s ease-out;
}

.notification-modal.success {
  border-color: #10b981;
  box-shadow: 0 25px 50px rgba(16, 185, 129, 0.2);
}

.notification-modal.error {
  border-color: #ef4444;
  box-shadow: 0 25px 50px rgba(239, 68, 68, 0.2);
}

.notification-modal.warning {
  border-color: #f59e0b;
  box-shadow: 0 25px 50px rgba(245, 158, 11, 0.2);
}

.notification-modal.info {
  border-color: #3b82f6;
  box-shadow: 0 25px 50px rgba(59, 130, 246, 0.2);
}

.notification-header {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-icon {
  font-size: 24px;
  margin-right: 12px;
}

.notification-modal.success .notification-icon {
  color: #10b981;
}

.notification-modal.error .notification-icon {
  color: #ef4444;
}

.notification-modal.warning .notification-icon {
  color: #f59e0b;
}

.notification-modal.info .notification-icon {
  color: #3b82f6;
}

.notification-header h3 {
  flex: 1;
  margin: 0;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.notification-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.notification-close:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}

.notification-body {
  padding: 20px;
}

.notification-body p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.5;
}

.notification-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: flex-end;
}

.notification-footer .btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.notification-footer .btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

@keyframes notificationSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.settings-container {
  padding: 20px;
  min-height: calc(100vh - 140px);
}

/* Compact Header */
.settings-header.compact {
  margin-bottom: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border: 2px solid var(--border-color);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(212, 175, 55, 0.1);
  position: relative;
  overflow: hidden;
}

.settings-header.compact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--primary-color);
  opacity: 0.6;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.page-title {
  font-size: 28px;
  font-weight: 800;
  margin: 0;
  color: var(--primary-color);
  text-shadow: 0 0 10px var(--luxury-shadow);
}

.header-stats {
  display: flex;
  gap: 12px;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 600;
  color: #fff;
}

.stat-badge i {
  color: var(--primary-color);
  font-size: 14px;
}

.page-subtitle {
  font-size: 14px;
  color: #888;
  margin: 0;
}

/* Settings Content */
.settings-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Navigation */
.settings-nav {
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 8px;
  justify-content: space-between;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
  min-height: 56px;
}

.settings-nav::-webkit-scrollbar {
  height: 4px;
}

.settings-nav::-webkit-scrollbar-track {
  background: transparent;
}

.settings-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #888;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  min-height: 38px;
  line-height: 1.2;
  flex: 1;
  justify-content: center;
}

.nav-tab:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #fff;
}

.nav-tab.active {
  background: var(--primary-color);
  color: #000;
  box-shadow: 0 2px 8px var(--luxury-shadow);
}

.nav-tab i {
  font-size: 14px;
}

/* Settings Panel */
.settings-panel {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  min-height: 400px;
}

.tab-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.tab-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.tab-info {
  flex: 1;
}

.tab-header h2 {
  font-size: 20px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 4px;
}

.tab-header p {
  color: #888;
  margin: 0;
  font-size: 14px;
}

.tab-stats {
  flex-shrink: 0;
}

/* Exchanges Table */
.exchanges-table {
  overflow-x: auto;
}

.settings-table {
  width: 100%;
  border-collapse: collapse;
}

.settings-table th {
  text-align: left;
  padding: 8px 6px;
  font-size: 11px;
  font-weight: 600;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-table td {
  padding: 8px 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  vertical-align: middle;
}

.exchange-row {
  transition: all 0.3s ease;
}

.exchange-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.exchange-row.enabled {
  background: rgba(16, 185, 129, 0.05);
}

.exchange-row.disabled {
  opacity: 0.6;
  background: rgba(128, 128, 128, 0.05);
}

.exchange-cell .exchange-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.exchange-info img {
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.exchange-name {
  display: block;
  font-size: 13px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 2px;
}



/* Config Cell */
.config-cell {
  max-width: 320px;
}

/* Status Cell */
.status-cell {
  width: 120px;
  min-width: 100px;
  text-align: center;
}

.api-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 12px;
  padding: 4px 0;
}

.api-status .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.api-status.valid .status-dot {
  background: #10b981;
  animation: pulse 2s infinite;
  box-shadow: 0 0 4px rgba(16, 185, 129, 0.5);
}

.api-status.invalid .status-dot {
  background: #ef4444;
  box-shadow: 0 0 4px rgba(239, 68, 68, 0.5);
}

.api-status.testing .status-dot {
  background: #f59e0b;
  animation: pulse 1s infinite;
  box-shadow: 0 0 4px rgba(245, 158, 11, 0.5);
}

.api-status.error .status-dot {
  background: #ef4444;
  box-shadow: 0 0 4px rgba(239, 68, 68, 0.5);
}

.api-status.not-configured .status-dot {
  background: #6b7280;
  box-shadow: 0 0 4px rgba(107, 114, 128, 0.3);
}

.api-status .status-text {
  color: #ccc;
  font-weight: 500;
  font-size: 11px;
  white-space: nowrap;
}

.api-status.valid {
  background: rgba(16, 185, 129, 0.15);
  border-radius: 12px;
  padding: 6px 12px;
}

.api-status.valid .status-text {
  color: #fff;
}

.api-status.invalid,
.api-status.error,
.api-status.disabled {
  background: rgba(239, 68, 68, 0.15);
  border-radius: 12px;
  padding: 6px 12px;
}

.api-status.invalid .status-text,
.api-status.error .status-text,
.api-status.disabled .status-text {
  color: #fff;
}

.api-status.not-configured {
  background: rgba(245, 158, 11, 0.15);
  border-radius: 12px;
  padding: 6px 12px;
}

.api-status.not-configured .status-text {
  color: #fff;
}

.api-inputs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.save-api-btn {
  background: var(--primary-color);
  border: none;
  border-radius: 6px;
  color: #000;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin-top: 4px;
  box-shadow: 0 2px 8px var(--luxury-shadow);
}

.save-api-btn:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--luxury-shadow);
}

.save-api-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.api-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: #fff;
  padding: 6px 10px;
  font-size: 12px;
  width: 100%;
  transition: all 0.3s ease;
}

.api-input:focus {
  outline: none;
  border-color: #10b981;
  background: rgba(255, 255, 255, 0.15);
}


.disabled-text {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

/* API Config Cell */
.api-config-cell {
  width: 100px;
  text-align: center;
}

/* Action Cell */
.action-cell {
  width: 80px;
  text-align: center;
}

.config-btn {
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 6px;
  color: #3b82f6;
  padding: 6px 8px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.config-btn:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.3);
}

.config-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-label {
  font-size: 13px;
  font-weight: 500;
  color: #ccc;
}

.toggle-switch {
  width: 40px;
  height: 22px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 11px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.toggle-switch.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 0 10px var(--luxury-shadow);
}

.toggle-switch .toggle-handle {
  width: 18px;
  height: 18px;
  background: #fff;
  border-radius: 9px;
  position: absolute;
  top: 1px;
  left: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-switch.active .toggle-handle {
  transform: translateX(18px);
}

.toggle-switch.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(128, 128, 128, 0.1);
}

.toggle-switch.compact {
  width: 36px;
  height: 20px;
  border-radius: 10px;
}

.toggle-switch.compact .toggle-handle {
  width: 16px;
  height: 16px;
  border-radius: 8px;
}

.toggle-switch.compact.active .toggle-handle {
  transform: translateX(16px);
}

.test-btn {
  background: rgba(16, 185, 129, 0.2);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 6px;
  color: #10b981;
  padding: 6px 10px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-btn:hover:not(:disabled) {
  background: rgba(16, 185, 129, 0.3);
}

.test-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Settings Grid - Card Layout */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.setting-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  display: flex;
  gap: 15px;
}

.setting-card:hover {
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.setting-card.wide {
  grid-column: span 2;
}

.card-icon {
  width: 40px;
  height: 40px;
  background: rgba(16, 185, 129, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-icon i {
  font-size: 18px;
  color: #10b981;
}

.card-content {
  flex: 1;
  min-width: 0;
}

.card-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  margin: 0 0 4px 0;
}

.card-content p {
  font-size: 12px;
  color: #888;
  margin: 0 0 10px 0;
}

.compact-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #fff;
  padding: 6px 10px;
  font-size: 13px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.compact-select:focus {
  outline: none;
  border-color: #10b981;
}

.compact-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #fff;
  padding: 6px 10px;
  font-size: 13px;
  width: 80px;
  transition: all 0.3s ease;
}

.compact-input.full {
  width: 100%;
}

.compact-input:focus {
  outline: none;
  border-color: #10b981;
  background: rgba(255, 255, 255, 0.15);
}

/* Profile Tab Styles */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.profile-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 80px;
}

.profile-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
}

.form-input, .form-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #10b981;
  background: rgba(255, 255, 255, 0.15);
}

.form-input::placeholder {
  color: #666;
}

/* Wallet Info Styles - Elegant Design */
.wallet-info {
  margin-top: 25px;
}

.wallet-card {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.03) 0%, rgba(0, 0, 0, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(212, 175, 55, 0.15);
  border-radius: 24px;
  padding: 35px;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    0 0 100px rgba(212, 175, 55, 0.03);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Elegant gradient overlay */
.wallet-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* Top accent line */
.wallet-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    var(--primary-color), 
    transparent
  );
  opacity: 0.8;
}

.wallet-card:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 30px 60px -15px rgba(0, 0, 0, 0.9),
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    0 0 120px rgba(212, 175, 55, 0.05);
  border-color: rgba(212, 175, 55, 0.25);
}

/* Wallet Content Structure */
.wallet-content {
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.wallet-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.wallet-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(212, 175, 55, 0.05) 100%);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  transition: all 0.3s ease;
}

.wallet-icon::before {
  content: '';
  position: absolute;
  inset: -1px;
  border-radius: 16px;
  padding: 1px;
  background: linear-gradient(135deg, var(--primary-color), transparent);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.wallet-card:hover .wallet-icon::before {
  opacity: 0.6;
}

.wallet-icon i {
  font-size: 28px;
  background: linear-gradient(135deg, var(--primary-color), #e5c643);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.2));
}

.wallet-details {
  flex: 1;
}

.wallet-type {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
  letter-spacing: -0.02em;
}

.wallet-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #10b981;
  font-weight: 600;
}

.wallet-status .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  box-shadow: 0 0 12px rgba(16, 185, 129, 0.8);
  animation: pulse-green 2s infinite;
}

/* Address Section */
.wallet-address-container {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 20px;
}

.address-label {
  font-size: 12px;
  font-weight: 600;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  margin-bottom: 10px;
}

.wallet-card .wallet-address {
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.wallet-card .address-text {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
  font-size: 14px;
  color: var(--text-muted);
  letter-spacing: 0.02em;
  word-break: break-word !important;
  overflow-wrap: break-word;
  white-space: normal !important;
  word-spacing: normal !important;
  display: block !important;
  writing-mode: horizontal-tb !important;
  width: 100%;
}

.copy-button-container {
  display: flex;
  justify-content: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.copy-btn {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 8px;
  color: var(--primary-color);
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-size: 14px;
  font-weight: 500;
}

.copy-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(212, 175, 55, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.copy-btn:hover {
  background: rgba(212, 175, 55, 0.15);
  border-color: rgba(212, 175, 55, 0.3);
  transform: translateY(-1px);
}

.copy-btn:hover::before {
  width: 100%;
  height: 100%;
}

.copy-btn:active {
  transform: translateY(0);
}

.copy-btn.copied {
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
  color: #10b981;
  animation: copy-success 0.3s ease;
}

@keyframes copy-success {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.copy-btn i {
  font-size: 14px;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.copy-btn.copied i {
  transform: scale(1.1);
}

/* Wallet Actions */
.wallet-actions {
  display: flex;
  justify-content: flex-end;
}

@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

.disconnect-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-muted);
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: 0.02em;
}

.disconnect-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
  transform: translateY(-1px);
}

.disconnect-btn:active {
  transform: translateY(0);
}

.disconnect-btn i {
  font-size: 14px;
  transition: transform 0.3s ease;
}

.disconnect-btn:hover i {
  transform: rotate(-12deg);
}

/* Profile Actions */
.profile-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

/* Save Section */
.save-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.save-btn {
  background: var(--primary-color);
  border: none;
  border-radius: 12px;
  color: #000;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px var(--luxury-shadow);
}

.save-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px var(--luxury-shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-container {
    padding: 10px;
  }
  
  .settings-header.compact {
    padding: 15px;
    margin-bottom: 15px;
  }
  
  .header-top {
    flex-direction: column;
    gap: 10px;
  }
  
  .page-title {
    font-size: 22px;
  }
  
  .tab-title-section {
    flex-direction: column;
    gap: 15px;
  }
  
  .settings-table {
    font-size: 12px;
  }
  
  .settings-table th {
    padding: 8px 6px;
    font-size: 11px;
  }
  
  .settings-table td {
    padding: 8px 6px;
  }
  
  .exchange-info img {
    width: 24px;
    height: 24px;
  }
  
  .exchange-name {
    font-size: 12px;
  }
  
  .api-input {
    font-size: 11px;
    padding: 4px 8px;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .setting-card {
    padding: 15px;
  }
  
  .setting-card.wide {
    grid-column: span 1;
  }
  
  .card-icon {
    width: 36px;
    height: 36px;
  }
  
  .card-icon i {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .settings-container {
    padding: 8px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .stat-badge {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .settings-nav {
    padding: 6px;
    gap: 3px;
    min-height: 48px;
  }
  
  .nav-tab {
    padding: 8px 12px;
    font-size: 12px;
    min-height: 34px;
  }
  
  .settings-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .status-cell {
    display: none;
  }
  
  /* Show status inline on mobile */
  .exchange-info::after {
    content: attr(data-status);
    font-size: 10px;
    color: #888;
    margin-left: 8px;
  }
  
  .toggle-label {
    display: none;
  }
  
  .save-api-btn {
    font-size: 11px;
    padding: 6px 10px;
  }
  
  .save-section {
    margin-top: 15px;
    padding-top: 15px;
  }
  
  .save-btn {
    padding: 10px 20px;
    font-size: 13px;
    width: 100%;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  animation: modalSlideIn 0.3s ease-out;
  margin-bottom: 250px;
  margin-top: 100px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 100px 30px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.modal-exchange-logo {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  flex-shrink: 0;
}

.modal-title h3 {
  font-size: 20px;
  font-weight: 700;
  color: #fff;
  margin: 0 0 4px 0;
}

.modal-title p {
  font-size: 14px;
  color: #888;
  margin: 0;
}

.modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: #fff;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 25px 30px;
}

.api-status-display {
  margin-bottom: 25px;
  display: flex;
  justify-content: center;
}

.api-status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid;
}

.api-status-badge.valid {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.3);
  color: #10b981;
}

.api-status-badge.invalid {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.api-status-badge.testing {
  background: rgba(245, 158, 11, 0.2);
  border-color: rgba(245, 158, 11, 0.3);
  color: #f59e0b;
}

.api-status-badge.error {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.api-status-badge.not-configured {
  background: rgba(107, 114, 128, 0.2);
  border-color: rgba(107, 114, 128, 0.3);
  color: #6b7280;
}

.modal-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
}

.modal-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.modal-input:focus {
  outline: none;
  border-color: #10b981;
  background: rgba(255, 255, 255, 0.15);
}

.modal-input::placeholder {
  color: #666;
}

.api-info {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  font-size: 13px;
  color: #ccc;
}

.info-item i {
  color: #3b82f6;
  margin-top: 2px;
  flex-shrink: 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 30px 25px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: #fff;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px var(--luxury-shadow);
}

.btn-primary {
  background: var(--primary-color);
  border: none;
  border-radius: 8px;
  color: #000;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px var(--luxury-shadow);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px var(--luxury-shadow);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-delete {
  background: #ef4444;
  border: none;
  border-radius: 8px;
  color: #fff;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-delete:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.btn-delete:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Mobile Modal Styles */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-content {
    max-width: 100%;
  }
  
  .modal-header {
    padding: 20px 20px 15px;
  }
  
  .modal-exchange-logo {
    width: 40px;
    height: 40px;
  }
  
  .modal-title h3 {
    font-size: 18px;
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .modal-footer {
    padding: 15px 20px 20px;
    flex-direction: column;
  }
  
  .btn-secondary,
  .btn-primary,
  .btn-delete {
    width: 100%;
    justify-content: center;
  }
  
  .config-btn {
    font-size: 11px;
    padding: 5px 8px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .profile-actions {
    flex-direction: column;
  }
  
  .wallet-card {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .wallet-details {
    text-align: center;
  }
  
  .disconnect-btn {
    align-self: center;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* Searchable Select Styles */
.searchable-select {
  position: relative;
  width: 100%;
}

.select-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  transition: all 0.3s ease;
  min-height: 48px;
}

.select-input:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.select-input.open {
  border-color: var(--primary-color);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.selected-value {
  flex: 1;
  text-align: left;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.select-input i {
  color: #888;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.dropdown-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid var(--primary-color);
  border-top: none;
  border-radius: 0 0 8px 8px;
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
  animation: dropdownSlideIn 0.2s ease-out;
  backdrop-filter: blur(20px);
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-box {
  position: relative;
  padding: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 35px 8px 12px;
  color: #fff;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.15);
}

.search-input::placeholder {
  color: #666;
}

.search-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 14px;
  pointer-events: none;
}

.options-list {
  max-height: 240px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) transparent;
}

.options-list::-webkit-scrollbar {
  width: 4px;
}

.options-list::-webkit-scrollbar-track {
  background: transparent;
}

.options-list::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 2px;
}

.option-item {
  padding: 12px 16px;
  color: #ccc;
  cursor: pointer;
  font-size: 14px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.option-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.option-item.selected {
  background: rgba(212, 175, 55, 0.2);
  color: var(--primary-color);
  position: relative;
}

.option-item.selected::after {
  content: '✓';
  font-weight: bold;
  color: var(--primary-color);
}

.option-item.no-results {
  color: #666;
  cursor: default;
  font-style: italic;
  text-align: center;
}

.option-item.no-results:hover {
  background: transparent;
  color: #666;
}

/* Enhanced form input styles for consistency */
.form-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
  min-height: 48px;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.15);
}

.form-input::placeholder {
  color: #666;
}

.form-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
  min-height: 48px;
  cursor: pointer;
}

.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.15);
}

.form-select option {
  background: #1a1a1a;
  color: #fff;
  padding: 8px;
}

/* Mobile responsive adjustments for searchable select */
@media (max-width: 768px) {
  .dropdown-container {
    max-height: 250px;
  }
  
  .search-box {
    padding: 10px;
  }
  
  .option-item {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .selected-value {
    font-size: 13px;
  }
}

/* Readonly and Validation Styles */
.form-input.readonly {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: #888;
  cursor: not-allowed;
  opacity: 0.7;
}

.field-note {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #888;
  margin-top: 4px;
  font-style: italic;
}

.field-note i {
  color: #666;
  font-size: 11px;
}

/* Confirm Password Section Styling */
.confirm-password-section {
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-top: 30px;
}

.confirm-password-section label {
  color: #ef4444 !important;
  font-weight: 700;
}

.confirm-password-section .field-note {
  color: #ef4444;
}

.confirm-password-section .field-note i {
  color: #ef4444;
}

/* Input Validation Wrapper */
.input-validation-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.validation-indicator {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.validation-icon {
  font-size: 16px;
  animation: fadeIn 0.3s ease;
}

.validation-icon.checking {
  color: #f59e0b;
}

.validation-icon.valid {
  color: #10b981;
}

.validation-icon.invalid {
  color: #ef4444;
}

.validation-message {
  display: block;
  font-size: 12px;
  margin-top: 4px;
  font-weight: 500;
  animation: slideDown 0.3s ease;
}

.validation-message.checking {
  color: #f59e0b;
}

.validation-message.valid {
  color: #10b981;
}

.validation-message.invalid {
  color: #ef4444;
}

/* Input validation border states */
.form-input.valid {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.form-input.invalid {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.form-input.valid:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-input.invalid:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Input with validation indicator padding */
.input-validation-wrapper .form-input {
  padding-right: 45px;
}

/* Error input styling */
.form-input.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

/* Error message styling */
.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Help text styling */
.help-text {
  color: #6c757d;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 2FA Modal Styles */
.two-fa-modal {
  max-width: 600px;
}

.modal-icon {
  font-size: 20px;
  color: var(--primary-color);
}

/* 2FA Setup Styles */
.two-fa-setup {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setup-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-step {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.step-number {
  background: var(--primary-color);
  color: #000;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content h4 {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.step-content p {
  color: #ccc;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

/* 2FA Verify Styles */
.two-fa-verify {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.qr-section {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.qr-code {
  flex-shrink: 0;
  background: #fff;
  padding: 16px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-code img {
  width: 160px;
  height: 160px;
  display: block;
}

.qr-instructions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.qr-instructions h4 {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.qr-instructions p {
  color: #ccc;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.manual-entry {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.manual-entry p {
  margin: 0 0 8px 0;
}

.manual-entry p:first-child {
  font-weight: 600;
  color: #fff;
}

.manual-key {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  margin-top: 8px;
}

.manual-key code {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: var(--primary-color);
  background: none;
  border: none;
  letter-spacing: 1px;
}

.copy-key-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  color: #ccc;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.copy-key-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.verify-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
}

.totp-input {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 4px;
  font-family: 'Courier New', monospace;
}

.input-note {
  color: #888;
  font-size: 12px;
  text-align: center;
  margin-top: 8px;
}

/* 2FA Backup Codes Styles */
.two-fa-backup {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.backup-warning {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 12px;
  padding: 16px;
}

.warning-icon {
  color: #f59e0b;
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.warning-content h4 {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.warning-content p {
  color: #ccc;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.backup-codes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
}

.backup-code {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
}

.backup-code code {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  letter-spacing: 1px;
}

.backup-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.download-btn,
.copy-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
}

/* 2FA Disable Styles */
.two-fa-disable {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.disable-warning {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  padding: 16px;
}

.disable-warning .warning-icon {
  color: #ef4444;
}

.verification-methods {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.method-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
}

.method-option {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.method-option:hover {
  background: rgba(255, 255, 255, 0.05);
}

.method-option input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

.method-option span {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.btn-danger {
  background: #ef4444;
  border: none;
  border-radius: 8px;
  color: #fff;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.btn-danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Mobile Responsive for 2FA Modal */
@media (max-width: 768px) {
  .two-fa-modal {
    max-width: 100%;
  }
  
  .qr-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .qr-code img {
    width: 140px;
    height: 140px;
  }
  
  .backup-codes {
    grid-template-columns: repeat(2, 1fr);
    padding: 16px;
  }
  
  .backup-actions {
    flex-direction: column;
  }
  
  .download-btn,
  .copy-btn {
    width: 100%;
    justify-content: center;
  }
  
  .info-step {
    padding: 12px;
  }
  
  .step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .step-content h4 {
    font-size: 14px;
  }
  
  .step-content p {
    font-size: 13px;
  }
}

/* MT5 Specific Styles */
.mt5-tab {
  /* Inherit from existing tab styles */
}

.mt5-form {
  max-width: 800px;
}

.mt5-status {
  margin-top: 20px;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid;
}

.mt5-status.success {
  background: rgba(34, 197, 94, 0.1);
  border-left-color: #22c55e;
  color: #22c55e;
}

.mt5-status.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: #ef4444;
  color: #ef4444;
}

.mt5-status .status-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  margin-bottom: 16px;
}

.mt5-status .status-header i {
  font-size: 20px;
}

.mt5-status .account-info {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.mt5-status .account-info h4 {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.mt5-status .info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.mt5-status .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mt5-status .info-item:last-child {
  border-bottom: none;
}

.mt5-status .info-item label {
  color: #888;
  font-size: 14px;
  font-weight: 500;
}

.mt5-status .info-item span {
  color: #fff;
  font-weight: 600;
  text-align: right;
}

.mt5-status .error-message {
  color: #ef4444;
  font-size: 14px;
  margin-top: 8px;
  padding: 12px;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 6px;
}

.toggle-password {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.toggle-password:hover {
  color: var(--primary-color);
  background: rgba(212, 175, 55, 0.1);
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color);
  color: #000;
}

.btn-primary:hover:not(:disabled) {
  background: #e6c468;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.btn-success {
  background: #22c55e;
  color: #fff;
}

.btn-success:hover:not(:disabled) {
  background: #16a34a;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.btn-secondary {
  background: #6b7280;
  color: #fff;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

/* Multiple MT5 Accounts Styles */
.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.account-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.account-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.account-name h4 {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.account-login {
  color: #888;
  font-size: 12px;
}

.account-actions {
  display: flex;
  gap: 8px;
}

.btn-icon {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.6);
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-icon:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: rgba(255, 255, 255, 0.4);
}

.btn-icon.btn-danger:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border-color: #ef4444;
}

.account-info {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.info-row .label {
  color: #888;
  font-size: 12px;
}

.info-row .value {
  color: #fff;
  font-size: 12px;
  font-weight: 600;
}

.info-row .status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.info-row .status.active {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.info-row .status.inactive {
  background: rgba(107, 114, 128, 0.2);
  color: #6b7280;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #888;
}

.empty-state i {
  font-size: 48px;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #fff;
  margin-bottom: 8px;
  font-size: 20px;
}

.empty-state p {
  margin-bottom: 0;
  font-size: 14px;
}

/* MT5 Modal Form Styles */
.mt5-modal {
  max-width: 400px !important;
  max-height: none !important;
}

.mt5-modal .form-grid {
  display: grid;
  gap: 12px;
}

.mt5-modal .form-group label {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
}

.mt5-modal .form-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  color: #fff;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.mt5-modal .form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.mt5-modal .form-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.mt5-modal .input-with-toggle {
  position: relative;
}

.mt5-modal .input-with-toggle .form-input {
  padding-right: 50px;
}

.mt5-modal .toggle-password {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.mt5-modal .toggle-password:hover {
  color: var(--primary-color);
  background: rgba(212, 175, 55, 0.1);
}

.mt5-modal .form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  flex-wrap: wrap;
}

.mt5-modal .btn {
  flex: 1;
  min-width: 120px;
  justify-content: center;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mt5-modal .btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #f59e0b 100%);
  border: none;
  color: #000;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.mt5-modal .btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.mt5-modal .btn-success {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.mt5-modal .btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
}

.mt5-modal .btn-secondary {
  background: rgba(107, 114, 128, 0.2);
  border: 1px solid rgba(107, 114, 128, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

.mt5-modal .btn-secondary:hover:not(:disabled) {
  background: rgba(107, 114, 128, 0.3);
  border-color: rgba(107, 114, 128, 0.5);
  color: #fff;
}

/* MT5 Status Display */
.mt5-modal .mt5-status {
  margin-top: 16px;
  padding: 16px;
  border-radius: 8px;
  border-left: 3px solid;
}

.mt5-modal .mt5-status.success {
  background: rgba(34, 197, 94, 0.1);
  border-color: #22c55e;
}

.mt5-modal .mt5-status.error {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
}

.mt5-modal .mt5-status .status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.mt5-modal .mt5-status .account-info h4 {
  font-size: 16px;
  margin-bottom: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.mt5-modal .mt5-status .info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.mt5-modal .mt5-status .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.mt5-modal .mt5-status .info-item:last-child {
  border-bottom: none;
}

.mt5-modal .mt5-status .info-item label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.mt5-modal .mt5-status .info-item span {
  font-size: 13px;
  font-weight: 600;
  color: #fff;
}

.mt5-modal .error-message {
  color: #ef4444;
  font-size: 14px;
  margin-top: 8px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .mt5-modal .form-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .mt5-modal .btn {
    width: 100%;
    flex: none;
  }
  
  .mt5-modal .mt5-status .info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}